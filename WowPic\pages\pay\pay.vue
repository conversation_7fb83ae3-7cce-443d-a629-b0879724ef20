<template>
	<view class="pay-container">
		<!-- 顶部安全区域 -->
		<view class="safe-area"></view>

		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="navbar-left" @click="goBack">
				<view class="back-icon"></view>
			</view>

		</view>

		<!-- 简化背景装饰 -->
		<view class="background-decoration">
			<view class="floating-elements">
				<!-- 金币图标 -->
				<view v-for="i in 6" :key="`coin-${i}`" class="floating-coin" :class="`coin-${i}`">
					<image src="/static/coins.png" lazy-load></image>
				</view>
			</view>
		</view>

		<!-- 页面内容 -->
		<scroll-view class="content-scroll" scroll-y="true" show-scrollbar="false">
			<!-- 头部占位符，为固定头部留出空间 -->
			<view class="header-placeholder"></view>

			<!-- 新用户特惠区域 -->
			<view v-if="showNewUserOffer" class="new-user-section">
				<view class="special-badge">🎉 新用户专享</view>
				<view class="new-user-card" @click="selectNewUserOffer">
					<view class="offer-badge">限时优惠</view>
					<view class="offer-content">
						<view class="offer-left">
							<view class="offer-title">首充特惠礼包（仅限当天）</view>
							<view class="coin-display">
								<image class="coin-icon" src="/static/coins.png" lazy-load></image>
								<text class="coin-amount">100</text>
								<text class="reward-label">哇图币</text>
							</view>
						</view>
						<view class="offer-right">
							<view class="special-price">¥0.99</view>
							<view class="save-amount">省¥5.01</view>
						</view>
					</view>
					<view class="offer-highlight">🔥 超值首充，错过不再有！</view>
				</view>
			</view>

			<!-- 观看广告获取哇图币 -->
			<view class="ad-section">
				<view class="section-header">
					<view class="section-title">免费获取</view>
				</view>
				<view class="ad-option" @click="watchAd">
					<view class="ad-content">
						<view class="ad-left">
							<view class="ad-icon">📺</view>
							<view class="ad-info">
								<view class="ad-title">观看激励广告</view>
								<view class="ad-desc">观看30秒广告视频</view>
							</view>
						</view>
						<view class="ad-right">
							<view class="ad-reward">
								<image class="ad-coin-icon" src="/static/coins.png" lazy-load></image>
								<text class="ad-coin-amount">+5</text>
							</view>
							<view class="ad-limit">每日限3次</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 常规充值档位 -->
			<view class="regular-section">
				<view class="section-header">
					<view class="section-title">🎁 选择奖励档位</view>
				</view>
				<view class="recharge-options">
					<view
						v-for="(option, index) in rechargeOptions"
						:key="index"
						class="recharge-option"
						:class="{ 'option-selected': selectedOption === index }"
						@click="selectOption(index)">

						<!-- 省钱标签 -->
						<view v-if="option.savedAmount > 0" class="saved-badge">省{{ option.savedAmount.toFixed(1) }}元</view>

						<view class="option-content">
							<!-- 第一行：主要信息区域 -->
							<view class="main-info-area">
								<view class="coin-display">
									<image class="main-coin-icon" src="/static/coins.png" lazy-load></image>
									<view class="coin-amount-large">{{ option.coins }}</view>
									<view class="reward-label">哇图币</view>
									<view class="unlock-info">≈{{ Math.round(option.coins / 30) }}次生成</view>
								</view>
							</view>

							<!-- 第一行：现价信息区域 -->
							<view class="current-price-area">
								<view class="price-info">仅需¥{{ option.price }}</view>
							</view>

							<!-- 第二行：次要信息区域 -->
							<view class="secondary-info-area">
								<view v-if="option.bonus > 0" class="bonus-highlight">
									+{{ option.bonus }}币奖励
								</view>
								<view v-if="option.savedAmount > 0" class="saved-info">
									原价¥{{ option.originalPrice.toFixed(1) }}
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>


	</view>
</template>

<script>
	import request from '../../utils/request.js'

	export default {
		data() {
			return {
				// 充值选项
				rechargeOptions: [],
				selectedOption: -1,

				// 新用户特惠
				showNewUserOffer: true,
				selectedNewUser: false,

				// 用户信息
				userInfo: {},

				// 广告相关
				adWatchCount: 0,
				maxAdWatch: 3
			}
		},

		onLoad() {
			// 异步加载，避免阻塞页面渲染
			this.$nextTick(() => {
				this.loadUserInfo()
				this.loadRechargeOptions()
			})
		},

		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack()
			},

			// 加载用户信息
			async loadUserInfo() {
				try {
					const userInfo = uni.getStorageSync('userInfo')
					if (userInfo) {
						this.userInfo = userInfo
					}
				} catch (e) {
					console.error('获取用户信息失败', e)
				}
			},

			// 加载充值选项
			async loadRechargeOptions() {
				try {
					const options = await request.get('/wowpic/pay/options')
					if (!options || options.length === 0) {
						uni.showToast({ title: '暂无充值选项', icon: 'none' })
						return
					}

					// 处理充值选项数据，添加推荐和奖励信息
					this.rechargeOptions = options.map((option) => {
						const baseCoins = this.calculateBaseCoins(option.price_cny)
						const bonus = option.coins - baseCoins
						const bonusPercent = Math.round((bonus / baseCoins) * 100)

						// 计算省钱金额：哇图币数量 ÷ 100 = 原价，原价 - 实际价格 = 省钱金额
						const originalPrice = option.coins / 100
						const savedAmount = originalPrice - option.price_cny

						return {
							coins: option.coins,
							price: option.price_cny,
							bonus: bonus > 0 ? bonus : 0,
							bonusPercent: bonusPercent > 0 ? bonusPercent : 0,
							originalPrice: originalPrice,
							savedAmount: savedAmount > 0 ? savedAmount : 0,
							recommended: false, // 移除推荐标识
							recommendReason: ''
						}
					})

					// 检查是否显示新用户特惠
					this.checkNewUserOffer()

				} catch (e) {
					console.error('获取充值选项失败', e)
					uni.showToast({ title: '获取充值选项失败', icon: 'none' })
				}
			},

			// 计算基础币数（用于计算奖励）
			calculateBaseCoins(price) {
				// 假设基础比例是 1元 = 100币
				return Math.round(price * 100)
			},

			// 检查新用户特惠
			checkNewUserOffer() {
				// 检查是否有100币的选项（新用户特惠已使用的标志）
				const hasNewUserOption = this.rechargeOptions.some(option => option.coins === 100)
				this.showNewUserOffer = !hasNewUserOption
			},

			// 选择新用户特惠 - 点击即充值
			selectNewUserOffer() {
				this.selectedNewUser = true
				this.selectedOption = -1

				// 立即显示Loading并触发充值
				uni.showLoading({ title: '正在充值...' })
				this.processPayment(100)
			},

			// 选择充值选项 - 点击即充值
			selectOption(index) {
				this.selectedOption = index
				this.selectedNewUser = false

				const option = this.rechargeOptions[index]
				if (!option) return

				// 立即显示Loading并触发充值
				uni.showLoading({ title: '正在充值...' })
				this.processPayment(option.coins)
			},

			// 观看广告
			watchAd() {
				if (this.adWatchCount >= this.maxAdWatch) {
					uni.showToast({ title: '今日观看次数已用完', icon: 'none' })
					return
				}

				// TODO: 集成激励广告SDK
				uni.showToast({ title: '广告功能开发中', icon: 'none' })
			},

			// 处理支付流程（新的点击即充值逻辑）
			async processPayment(coins) {
				try {
					// 向后端申请充值订单
					const orderRes = await request.post('/wowpic/pay/recharge', { amount: coins })

					if (!orderRes || !orderRes.success) {
						uni.hideLoading()
						uni.showToast({ title: '创建订单失败', icon: 'none' })
						return
					}

					const payParams = orderRes.payment_params || {}

					// 调用微信小程序支付接口
					uni.requestPayment({
						...payParams,
						success: () => {
							uni.hideLoading()
							this.onPaySuccess(coins)
						},
						fail: (err) => {
							uni.hideLoading()
							this.onPayFail(err)
						}
					})
				} catch (e) {
					uni.hideLoading()
					console.error('充值处理出错', e)
					uni.showToast({ title: '充值失败，请重试', icon: 'none' })
				}
			},

			// 确认支付（保留原有方法，以防其他地方调用）
			async confirmPay() {
				if (this.selectedOption === -1 && !this.selectedNewUser) {
					uni.showToast({ title: '请选择充值金额', icon: 'none' })
					return
				}

				try {
					let selectedAmount

					if (this.selectedNewUser) {
						selectedAmount = 100 // 新用户特惠100币
					} else {
						selectedAmount = this.rechargeOptions[this.selectedOption].coins
					}

					// 向后端申请充值订单
					const orderRes = await request.post('/wowpic/pay/recharge', { amount: selectedAmount })

					if (!orderRes || !orderRes.success) {
						uni.showToast({ title: '创建订单失败', icon: 'none' })
						return
					}

					const payParams = orderRes.payment_params || {}

					// 显示加载提示
					uni.showLoading({
						title: '处理中...',
						mask: true
					})

					// 调用微信小程序支付接口
					uni.requestPayment({
						...payParams,
						success: () => {
							uni.hideLoading()
							this.onPaySuccess(selectedAmount)
						},
						fail: (err) => {
							uni.hideLoading()
							this.onPayFail(err)
						}
					})
				} catch (e) {
					uni.hideLoading()
					console.error('充值处理出错', e)
					uni.showToast({ title: '充值失败，请重试', icon: 'none' })
				}
			},

			// 支付成功处理
			onPaySuccess() {
				// 显示成功提示
				uni.showToast({
					title: '充值成功',
					icon: 'success',
					duration: 2000
				})

				// 延迟返回上一页
				setTimeout(() => {
					uni.navigateBack()
				}, 2000)
			},

			// 支付失败处理
			onPayFail(err) {
				console.error('支付失败', err)
				if (err.errMsg && err.errMsg.indexOf('cancel') > -1) {
					uni.showToast({ title: '支付已取消', icon: 'none' })
				} else {
					uni.showModal({
						title: '支付提示',
						content: '支付遇到问题，请稍后再试或联系客服',
						showCancel: false
					})
				}
			}
		}
	}
</script>

<style scoped>
	/* CSS变量定义 */
	:root {
		--primary-color: #7562FF;
		--secondary-color: #4A90E2;
		--text-dark: #333;
		--text-light: #666;
	}

	.pay-container {
		min-height: 100vh;
		background: linear-gradient(180deg, #F8F9FF 0%, #FFFFFF 100%);
		position: relative;
	}

	/* 安全区域 */
	.safe-area {
		height: var(--status-bar-height);
		background: transparent;
	}

	/* 自定义导航栏 */
	.custom-navbar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 88rpx;
		padding: 0 30rpx;
		background: transparent;
		position: fixed;
		top: 88rpx; /* 安全区域高度 */
		left: 0;
		right: 0;
		z-index: 100;
	}

	.navbar-left {
		width: 70rpx;
		height: 70rpx;
		border-radius: 35rpx;
		background-color: #FFFFFF;
		display: flex;
		justify-content: center;
		align-items: center;
		/* 增强阴影效果，使按钮在各种背景上更加明显 */
		box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.25), 0 0 6rpx rgba(0, 0, 0, 0.1);
		/* 添加细微的边框增强对比度 */
		border: 1rpx solid rgba(0, 0, 0, 0.05);
		/* 添加过渡效果使点击更流畅 */
		transition: transform 0.2s ease, box-shadow 0.2s ease;
	}

	.navbar-left:active {
		transform: scale(0.95);
		box-shadow: 0 3rpx 8rpx rgba(0, 0, 0, 0.2);
	}

	.back-icon {
		width: 36rpx;
		height: 36rpx;
		background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333333' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M15 18l-6-6 6-6'/%3E%3C/svg%3E");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}

	/* 头部占位符，为固定头部留出空间 */
	.header-placeholder {
		height: 90rpx; /* 与custom-navbar高度一致 */
	}

	/* 内容滚动区域 */
	.content-scroll {
		height: calc(100vh - var(--status-bar-height)); /* 88rpx安全区域 + 88rpx导航栏 */
		padding: 20rpx 24rpx 40rpx 24rpx; /* 增加底部padding，避免内容紧贴底部 */
		box-sizing: border-box;
		width: 100%;
		overflow-x: hidden;
		position: relative;
		z-index: 2;
	}

	/* 全屏背景装饰 */
	.background-decoration {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		pointer-events: none;
		z-index: 1;
		overflow: hidden;
	}

	.floating-elements {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		pointer-events: none;
	}

	/* 浮动金币 */
	.floating-coin {
		position: absolute;
		width: 48rpx;
		height: 48rpx;
		opacity: 0.2;
		animation: floatSimple 10s ease-in-out infinite;
	}

	.floating-coin image {
		width: 100%;
		height: 100%;
	}

	/* 金币位置 - 6个 */
	.coin-1 { top: 15%; left: 10%; animation-delay: 0s; }
	.coin-2 { top: 35%; right: 15%; animation-delay: 2s; }
	.coin-3 { top: 55%; left: 20%; animation-delay: 4s; }
	.coin-4 { top: 75%; right: 25%; animation-delay: 1s; }
	.coin-5 { top: 25%; left: 70%; animation-delay: 3s; }
	.coin-6 { top: 65%; right: 60%; animation-delay: 5s; }

	/* 增大移动范围的动画 */
	@keyframes floatSimple {
		0% {
			transform: translateY(0px) translateX(0px) rotate(0deg);
			opacity: 0.2;
		}
		25% {
			transform: translateY(-40rpx) translateX(30rpx) rotate(90deg);
			opacity: 0.35;
		}
		50% {
			transform: translateY(-60rpx) translateX(-20rpx) rotate(180deg);
			opacity: 0.4;
		}
		75% {
			transform: translateY(-30rpx) translateX(40rpx) rotate(270deg);
			opacity: 0.3;
		}
		100% {
			transform: translateY(0px) translateX(0px) rotate(360deg);
			opacity: 0.2;
		}
	}

	/* 区块标题样式 */
	.section-header {
		margin-bottom: 30rpx;
	}

	.special-badge {
		display: inline-block;
		background: linear-gradient(135deg, #FF6B9D, #FF8E9B);
		color: #FFFFFF;
		font-size: 24rpx;
		font-weight: bold;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		margin-bottom: 12rpx;
		border: 2rpx solid #333;
		box-shadow: 0 4rpx 8rpx rgba(255, 107, 157, 0.3);
	}

	.section-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 8rpx;
	}



	/* 新用户特惠区域 */
	.new-user-section {
		margin-bottom: 50rpx;
	}

	.new-user-card {
		background: linear-gradient(135deg, #FFE5EC, #FFF0F3);
		border: 4rpx solid #FF6B9D;
		border-radius: 24rpx;
		padding: 30rpx;
		position: relative;
		overflow: hidden;
		box-shadow: 0 12rpx 24rpx rgba(255, 107, 157, 0.2);
		transition: all 0.3s ease;
	}

	.new-user-card:active {
		transform: scale(0.98);
	}

	.offer-badge {
		position: absolute;
		top: -2rpx;
		right: 30rpx;
		background: linear-gradient(135deg, #FF4757, #FF6B9D);
		color: #FFFFFF;
		font-size: 22rpx;
		font-weight: bold;
		padding: 8rpx 20rpx;
		border-radius: 0 0 16rpx 16rpx;
		border: 2rpx solid #333;
		border-top: none;
		box-shadow: 0 4rpx 8rpx rgba(255, 71, 87, 0.3);
	}

	.offer-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 20rpx;
	}

	.offer-left {
		flex: 1;
	}

	.offer-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 8rpx;
	}



	.coin-display {
		display: flex;
		align-items: center;
		gap: 12rpx;
	}

	.coin-icon {
		width: 48rpx;
		height: 48rpx;
	}

	.coin-amount {
		font-size: 42rpx;
		font-weight: bold;
		color: var(--primary-color);
		margin-right: 8rpx;
	}

	.coin-unit {
		font-size: 24rpx;
		color: #666;
	}

	.offer-right {
		text-align: right;
	}

	.special-price {
		font-size: 40rpx;
		font-weight: bold;
		color: #FF4757;
		margin-bottom: 4rpx;
	}

	.save-amount {
		font-size: 22rpx;
		color: #FF4757;
		font-weight: bold;
	}

	.offer-highlight {
		text-align: center;
		font-size: 26rpx;
		font-weight: bold;
		color: #FF4757;
		background: rgba(255, 255, 255, 0.8);
		padding: 12rpx;
		border-radius: 16rpx;
		border: 2rpx solid #FF6B9D;
	}

	/* 常规充值区域 */
	.regular-section {
		margin-bottom: 50rpx;
	}

	.recharge-options {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.recharge-option {
		background: #FFFFFF;
		border: 3rpx solid #E0E0E0;
		border-radius: 20rpx;
		padding: 28rpx 24rpx;
		position: relative;
		transition: all 0.3s ease;
		box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.08);
		margin: 0 6rpx 10rpx 6rpx; /* 左右留出缩放空间，减少底部间距 */
	}

	.recharge-option.option-selected {
		border-color: var(--primary-color);
		background: linear-gradient(135deg, #F8F6FF, #FFFFFF);
		/* 移除选中状态的阴影和缩放效果，专注支付流程 */
	}

	.saved-badge {
		position: absolute;
		top: -3rpx;
		right: 20rpx;
		background: linear-gradient(135deg, #52C41A, #73D13D);
		color: #FFFFFF;
		font-size: 22rpx;
		font-weight: bold;
		padding: 8rpx 16rpx;
		border-radius: 0 0 16rpx 16rpx;
		border: 3rpx solid #333;
		border-top: none;
		box-shadow: 0 6rpx 12rpx rgba(82, 196, 26, 0.4);
		z-index: 2;
	}

	.option-content {
		display: grid;
		grid-template-columns: 1fr auto;
		grid-template-rows: auto auto;
		grid-template-areas:
			"main-info current-price"
			"secondary-info secondary-info";
		gap: 12rpx 16rpx;
		align-items: center;
		position: relative;
		min-height: 120rpx;
		padding: 4rpx 0;
	}

	/* Grid区域定义 */
	.main-info-area {
		grid-area: main-info;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-self: start;
		min-width: 0; /* 防止内容溢出 */
	}

	.current-price-area {
		grid-area: current-price;
		display: flex;
		align-items: center;
		justify-self: end;
		flex-shrink: 0;
	}

	.secondary-info-area {
		grid-area: secondary-info;
		display: flex;
		justify-content: space-between;
		align-items: center;
		gap: 12rpx;
		margin-top: 4rpx;
	}

	.coin-display {
		display: flex;
		align-items: flex-end;
		flex-wrap: nowrap; /* 防止换行 */
		gap: 8rpx 12rpx;
		width: 100%;
		min-width: 0; /* 防止内容溢出 */
		overflow: hidden; /* 防止内容溢出 */
	}

	.main-coin-icon {
		width: 58rpx;
		height: 58rpx;
	}

	.coin-amount-large {
		font-size: 54rpx;
		font-weight: 900;
		color: var(--primary-color);
		line-height: 1;
		text-shadow: 0 2rpx 4rpx rgba(117, 98, 255, 0.2);
	}

	.reward-label {
		font-size: 26rpx;
		font-weight: bold;
		color: var(--primary-color);
		opacity: 0.8;
		line-height: 1;
		white-space: nowrap;
		flex-shrink: 0; /* 防止压缩 */
	}

	.unlock-info {
		font-size: 28rpx;
		font-weight: bold;
		color: #52C41A;
		background: rgba(82, 196, 26, 0.1);
		padding: 3rpx 6rpx;
		border-radius: 6rpx;
		border: 1rpx solid rgba(82, 196, 26, 0.3);
		line-height: 1;
		white-space: nowrap;
		flex-shrink: 0; /* 防止压缩 */
	}

	.bonus-highlight {
		background: linear-gradient(135deg, #FF6B9D, #FF8E9B);
		color: #FFFFFF;
		font-size: 26rpx;
		font-weight: 900;
		padding: 6rpx 12rpx;
		border-radius: 12rpx;
		border: 2rpx solid #333;
		box-shadow: 0 3rpx 8rpx rgba(255, 107, 157, 0.4);
		display: inline-block;
		text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
		white-space: nowrap;
		flex-shrink: 0;
	}

	/* 价格对比容器 - 在Grid布局中使用 */

	.price-comparison {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
		gap: 4rpx;
	}

	.saved-info {
		font-size: 22rpx;
		color: #999;
		text-decoration: line-through;
		opacity: 0.8;
		font-weight: normal;
		white-space: nowrap;
		flex-shrink: 0;
	}

	.price-info {
		font-size: 30rpx;
		font-weight: bold;
		color: #FF6B9D;
		background: linear-gradient(135deg, rgba(255, 107, 157, 0.1), rgba(255, 255, 255, 0.9));
		padding: 8rpx 16rpx;
		border-radius: 16rpx;
		border: 2rpx solid #FF6B9D;
		box-shadow: 0 3rpx 10rpx rgba(255, 107, 157, 0.25);
		text-shadow: 0 1rpx 2rpx rgba(255, 107, 157, 0.3);
		white-space: nowrap;
		flex-shrink: 0;
	}

	/* 广告区域 */
	.ad-section {
		margin-bottom: 40rpx; /* 增加底部间距，避免内容紧贴滚动容器底部 */
	}

	.ad-option {
		background: linear-gradient(135deg, #F0FFF4, #FFFFFF);
		border: 3rpx solid #52C41A;
		border-radius: 20rpx;
		padding: 30rpx;
		transition: all 0.3s ease;
		box-shadow: 0 8rpx 16rpx rgba(82, 196, 26, 0.15);
	}

	.ad-option:active {
		transform: scale(0.98);
	}

	.ad-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.ad-left {
		display: flex;
		align-items: center;
		flex: 1;
	}

	.ad-icon {
		font-size: 48rpx;
		margin-right: 20rpx;
	}

	.ad-info {
		flex: 1;
	}

	.ad-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 8rpx;
	}

	.ad-desc {
		font-size: 24rpx;
		color: #666;
	}

	.ad-right {
		text-align: right;
	}

	.ad-reward {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		margin-bottom: 8rpx;
	}

	.ad-coin-icon {
		width: 28rpx;
		height: 28rpx;
		margin-right: 8rpx;
	}

	.ad-coin-amount {
		font-size: 32rpx;
		font-weight: bold;
		color: #52C41A;
	}

	.ad-limit {
		font-size: 22rpx;
		color: #999;
	}
	/* 页面进入动画 */
	.pay-container {
		animation: slideInUp 0.4s ease-out;
	}

	@keyframes slideInUp {
		from {
			transform: translateY(100%);
			opacity: 0;
		}
		to {
			transform: translateY(0);
			opacity: 1;
		}
	}

	/* 新用户卡片动画 */
	.new-user-card {
		animation: bounceIn 0.6s ease-out 0.2s both;
	}

	@keyframes bounceIn {
		0% {
			transform: scale(0.3);
			opacity: 0;
		}
		50% {
			transform: scale(1.05);
		}
		70% {
			transform: scale(0.9);
		}
		100% {
			transform: scale(1);
			opacity: 1;
		}
	}

	/* 未选中状态的呼吸动画（吸引用户点击） */
	.recharge-option:not(.option-selected) {
		animation: attractPulse 2.5s ease-in-out infinite;
	}

	@keyframes attractPulse {
		0% {
			box-shadow: 0 8rpx 24rpx rgba(117, 98, 255, 0.15);
			border-color: var(--secondary-color);
		}
		50% {
			box-shadow: 0 12rpx 32rpx rgba(117, 98, 255, 0.25);
			border-color: var(--primary-color);
		}
		100% {
			box-shadow: 0 8rpx 24rpx rgba(117, 98, 255, 0.15);
			border-color: var(--secondary-color);
		}
	}

	/* 未选中状态数字的呼吸动画 */
	.recharge-option:not(.option-selected) .coin-amount-large {
		animation: numberAttract 2.5s ease-in-out infinite;
	}

	@keyframes numberAttract {
		0% {
			color: var(--primary-color);
			text-shadow: 0 2rpx 4rpx rgba(117, 98, 255, 0.2);
		}
		50% {
			color: #9B8AFF;
			text-shadow: 0 4rpx 8rpx rgba(117, 98, 255, 0.4);
		}
		100% {
			color: var(--primary-color);
			text-shadow: 0 2rpx 4rpx rgba(117, 98, 255, 0.2);
		}
	}

	/* 按钮点击波纹效果 */
	.pay-button {
		position: relative;
		overflow: hidden;
	}

	.pay-button::before {
		content: '';
		position: absolute;
		top: 50%;
		left: 50%;
		width: 0;
		height: 0;
		border-radius: 50%;
		background: rgba(255, 255, 255, 0.3);
		transform: translate(-50%, -50%);
		transition: width 0.6s, height 0.6s;
	}

	.pay-button:active::before {
		width: 300rpx;
		height: 300rpx;
	}

	/* 金币图标旋转动画 */
	.coin-icon, .option-coin-icon, .ad-coin-icon {
		transition: transform 0.3s ease;
	}

	.recharge-option:hover .option-coin-icon,
	.new-user-card:hover .coin-icon,
	.ad-option:hover .ad-coin-icon {
		transform: rotate(360deg);
	}


</style>
